# Hệ Thống Floating UI - Tài Liệu Toàn Di<PERSON>n

## Tổng Quan Hệ Thống

Vocab sử dụng một hệ thống floating UI tiên tiến để quản lý các thành phần giao diện nổi như settings, guidance, notifications, tooltips, modals và dropdowns. Hệ thống được thiết kế với kiến trúc modular, hỗ trợ collision detection, responsive design và animation phong phú.

## Kiến Trúc Hệ Thống

### 1. C<PERSON>u Trúc Th<PERSON>

```
src/
├── components/floating-ui/
│   ├── floating-ui-manager.tsx      # Core manager component
│   ├── simple-enhanced-floating-buttons.tsx
│   └── index.ts                     # Exports
├── contexts/
│   ├── floating-ui-context.tsx      # Main context provider
│   └── simple-floating-context.tsx  # Simplified context
├── hooks/
│   ├── use-floating-ui.ts           # Advanced hooks
│   ├── use-floating-position.ts     # Position management
│   └── use-simple-floating.ts       # Simple implementation
├── types/floating-ui.ts             # TypeScript definitions
└── styles/floating-ui.css           # CSS styles
```

### 2. <PERSON><PERSON><PERSON>ần Chính

#### FloatingUIProvider

Context provider quản lý state toàn cục của floating elements, bao gồm:

-   Element registration và lifecycle management
-   Collision detection và resolution
-   Z-index management theo priority
-   Viewport tracking và responsive adjustments

#### FloatingUIManager

Component render chịu trách nhiệm:

-   Render visible elements với proper positioning
-   Animation handling với Framer Motion
-   Portal management cho DOM isolation
-   CSS class application theo type và priority

#### FloatingUIElement Types

Hệ thống hỗ trợ 7 loại floating element:

-   `settings`: Panel cài đặt ứng dụng
-   `guidance`: Hướng dẫn và onboarding
-   `notification`: Thông báo tạm thời
-   `tooltip`: Tooltip thông tin
-   `modal`: Dialog và modal
-   `dropdown`: Menu dropdown
-   `custom`: Tùy chỉnh theo nhu cầu

## API Documentation

### Core Hooks

#### useFloatingUIElement

Hook chính để tạo và quản lý floating element:

**Signature:**

```typescript
useFloatingUIElement(
  id: string,
  content: ReactNode,
  options: UseFloatingUIOptions
): FloatingUIElementReturn
```

**Parameters:**

-   `id`: Unique identifier cho element
-   `content`: React content để render
-   `options`: Configuration object với các thuộc tính:
    -   `type`: Loại element (settings, guidance, notification, etc.)
    -   `priority`: Mức độ ưu tiên (low, medium, high, critical)
    -   `position`: Vị trí (top-left, top-right, bottom-left, bottom-right, center, custom)
    -   `coordinates`: Tọa độ chính xác (top, bottom, left, right)
    -   `animation`: Cấu hình animation (type, duration, delay, easing)
    -   `persistent`: Không bị ẩn khi có collision
    -   `collisionDetection`: Bật/tắt collision detection
    -   `autoShow`: Tự động hiển thị khi mount

**Return Value:**

```typescript
{
  show: () => void;
  hide: () => void;
  toggle: () => void;
  isVisible: boolean;
  element: FloatingElement | undefined;
}
```

#### Specialized Hooks

**useFloatingSettings**

```typescript
useFloatingSettings(id: string, options?: SettingsOptions)
```

Tạo settings panel với các tùy chọn:

-   `showLanguageSelector`: Hiển thị language selector
-   `showThemeSelector`: Hiển thị theme selector
-   `showNotificationSettings`: Hiển thị notification settings

**useFloatingNotification**

```typescript
useFloatingNotification(id: string, content: ReactNode, options?: NotificationOptions)
```

Tạo notification với default position top-right và animation slide.

**useFloatingModal**

```typescript
useFloatingModal(id: string, content: ReactNode, options?: ModalOptions)
```

Tạo modal với center position và scale animation.

**useFloatingTooltip**

```typescript
useFloatingTooltip(id: string, content: ReactNode, options?: TooltipOptions)
```

Tạo tooltip với auto-positioning và fade animation.

### Context API

#### FloatingUIProvider Props

```typescript
interface FloatingUIProviderProps {
	children: ReactNode;
	config?: Partial<FloatingUIConfig>;
}
```

#### FloatingUIContext Methods

```typescript
interface FloatingUIContextType {
	// Element management
	register: (element: Omit<FloatingElement, 'visible'>) => void;
	unregister: (id: string) => void;
	show: (id: string) => void;
	hide: (id: string) => void;
	toggle: (id: string) => void;
	update: (id: string, updates: Partial<FloatingElement>) => void;

	// Z-index management
	bringToFront: (id: string) => void;
	sendToBack: (id: string) => void;

	// Bulk operations
	hideAll: (except?: string[]) => void;
	showAll: () => void;

	// Query methods
	getElement: (id: string) => FloatingElement | undefined;
	getVisibleElements: () => FloatingElement[];
	getElementsByType: (type: FloatingUIType) => FloatingElement[];
	getElementsByPriority: (priority: FloatingPriority) => FloatingElement[];

	// Collision management
	checkCollisions: (elementId: string) => string[];
	resolveCollisions: (elementId: string) => void;
	calculateOptimalPosition: (element: FloatingElement) => FloatingCoordinates;
}
```

## Configuration System

### Default Configuration

```typescript
const DEFAULT_FLOATING_CONFIG = {
	defaultZIndex: 1000,
	zIndexStep: 10,
	collisionMargin: 8,
	animationDefaults: {
		type: 'fade',
		duration: 200,
		delay: 0,
		easing: 'ease-in-out',
	},
	responsiveBreakpoints: {
		mobile: 768,
		tablet: 1024,
		desktop: 1280,
	},
	positionDefaults: {
		'top-left': { top: 16, left: 16 },
		'top-right': { top: 16, right: 16 },
		'bottom-left': { bottom: 16, left: 16 },
		'bottom-right': { bottom: 16, right: 16 },
		center: { top: 50, left: 50 },
	},
	priorityZIndex: {
		low: 1000,
		medium: 1100,
		high: 1200,
		critical: 1300,
	},
};
```

### Animation Types

-   `fade`: Opacity transition
-   `slide`: Slide from edge
-   `scale`: Scale from center
-   `bounce`: Spring animation với bounce effect

### Priority System

-   `critical`: Z-index 1300 (modals, important dialogs)
-   `high`: Z-index 1200 (notifications, alerts)
-   `medium`: Z-index 1100 (dropdowns, tooltips)
-   `low`: Z-index 1000 (background elements)

## Responsive Design

### Breakpoint System

-   **Mobile**: ≤ 768px
-   **Tablet**: 769px - 1024px
-   **Desktop**: ≥ 1025px

### Responsive Adjustments

-   **Mobile**: Reduced margins, full-width notifications, adjusted positioning
-   **Tablet**: Medium margins, optimized touch targets
-   **Desktop**: Full feature set, precise positioning

### CSS Classes

```css
.floating-ui-element[data-floating-type='settings'] {
	/* Mobile adjustments */
	@media (max-width: 768px) {
		bottom: 16px !important;
		right: 16px !important;
	}
}
```

## Collision Detection

### Algorithm

1. **Boundary Detection**: Kiểm tra element có vượt quá viewport không
2. **Element Overlap**: Tính toán overlap giữa các elements
3. **Priority Resolution**: Elements với priority cao hơn được ưu tiên
4. **Auto-positioning**: Tự động điều chỉnh vị trí để tránh collision

### Collision Resolution Strategies

-   **Offset**: Dịch chuyển element theo margin
-   **Stack**: Xếp chồng elements theo priority
-   **Hide**: Ẩn elements với priority thấp hơn
-   **Resize**: Điều chỉnh kích thước nếu cần thiết

## Performance Optimization

### Rendering Optimization

-   **Portal Rendering**: Sử dụng React Portal để tách DOM
-   **Lazy Registration**: Chỉ register khi cần thiết
-   **Memoization**: Cache calculations và styles
-   **Batch Updates**: Gom nhóm state updates

### Memory Management

-   **Auto Cleanup**: Tự động unregister khi component unmount
-   **Weak References**: Sử dụng WeakMap cho temporary data
-   **Event Debouncing**: Debounce resize và scroll events

## Accessibility Features

### ARIA Support

-   **aria-live**: Thông báo screen reader về dynamic content
-   **aria-describedby**: Liên kết tooltip với trigger element
-   **role**: Proper semantic roles cho từng element type

### Keyboard Navigation

-   **Focus Management**: Tự động focus management cho modals
-   **Escape Key**: Đóng floating elements với Escape
-   **Tab Trapping**: Trap focus trong modals

### High Contrast Mode

-   **Border Enhancement**: Thêm border trong high contrast mode
-   **Color Adjustments**: Điều chỉnh màu sắc cho accessibility

## Error Handling

### Graceful Degradation

-   **Fallback Rendering**: Render inline nếu portal fails
-   **Error Boundaries**: Catch và handle rendering errors
-   **Safe Defaults**: Sử dụng safe defaults cho invalid configs

### Debug Mode

-   **Console Warnings**: Cảnh báo về invalid configurations
-   **Visual Indicators**: Highlight collision trong development
-   **Performance Metrics**: Track rendering performance

## Ví Dụ Sử Dụng Thực Tế

### 1. Settings Panel

```typescript
// Component sử dụng settings panel
function MyPage() {
	const settingsContent = (
		<div className="p-4 bg-white rounded-lg shadow-lg">
			<h3>Cài đặt ứng dụng</h3>
			<LanguageSelector />
			<ThemeSelector />
			<NotificationSettings />
		</div>
	);

	const { show, hide, toggle, isVisible } = useFloatingSettings('app-settings', settingsContent, {
		position: 'bottom-right',
		coordinates: { bottom: 20, right: 20 },
		animation: { type: 'slide', duration: 300 },
	});

	return (
		<div>
			<Button onClick={toggle}>
				<Settings size={20} />
			</Button>
		</div>
	);
}
```

### 2. Notification System

```typescript
// Hook tùy chỉnh cho notifications
function useNotifications() {
	const showSuccess = (message: string) => {
		const content = (
			<div className="flex items-center gap-2 p-3 bg-green-100 text-green-800 rounded">
				<CheckCircle size={16} />
				{message}
			</div>
		);

		const { show } = useFloatingNotification(`success-${Date.now()}`, content, {
			position: 'top-right',
			animation: { type: 'slide', duration: 250 },
			autoShow: true,
		});

		// Auto hide after 3 seconds
		setTimeout(() => hide(), 3000);
	};

	const showError = (message: string) => {
		const content = (
			<div className="flex items-center gap-2 p-3 bg-red-100 text-red-800 rounded">
				<AlertCircle size={16} />
				{message}
			</div>
		);

		useFloatingNotification(`error-${Date.now()}`, content, {
			position: 'top-right',
			priority: 'high',
			persistent: true,
			autoShow: true,
		});
	};

	return { showSuccess, showError };
}
```

### 3. Guidance System

```typescript
// Onboarding guidance
function OnboardingGuide() {
	const steps = [
		{ id: 'welcome', content: <WelcomeStep />, position: 'center' },
		{ id: 'collections', content: <CollectionsStep />, position: 'bottom-left' },
		{ id: 'practice', content: <PracticeStep />, position: 'bottom-right' },
	];

	const [currentStep, setCurrentStep] = useState(0);

	const guidanceHooks = steps.map((step, index) =>
		useFloatingUIElement(`guidance-${step.id}`, step.content, {
			type: 'guidance',
			priority: 'high',
			position: step.position,
			animation: { type: 'fade', duration: 400 },
			autoShow: index === currentStep,
		})
	);

	const nextStep = () => {
		if (currentStep < steps.length - 1) {
			guidanceHooks[currentStep].hide();
			setCurrentStep(currentStep + 1);
			guidanceHooks[currentStep + 1].show();
		}
	};

	return null; // Guidance renders through floating system
}
```

### 4. Modal Dialog

```typescript
// Confirmation modal
function useConfirmDialog() {
	const [resolve, setResolve] = useState<((value: boolean) => void) | null>(null);

	const confirm = (message: string): Promise<boolean> => {
		return new Promise((res) => {
			setResolve(() => res);

			const content = (
				<div className="p-6 bg-white rounded-lg max-w-md">
					<h3 className="text-lg font-semibold mb-4">Xác nhận</h3>
					<p className="mb-6">{message}</p>
					<div className="flex gap-3 justify-end">
						<Button variant="outline" onClick={() => handleResponse(false)}>
							Hủy
						</Button>
						<Button onClick={() => handleResponse(true)}>Xác nhận</Button>
					</div>
				</div>
			);

			const { show } = useFloatingModal('confirm-dialog', content, {
				persistent: true,
				animation: { type: 'scale', duration: 200 },
			});

			show();
		});
	};

	const handleResponse = (response: boolean) => {
		if (resolve) {
			resolve(response);
			setResolve(null);
		}
	};

	return { confirm };
}
```

### 5. Tooltip System

```typescript
// Smart tooltip hook
function useSmartTooltip(content: ReactNode, options?: TooltipOptions) {
	const [triggerRef, setTriggerRef] = useState<HTMLElement | null>(null);

	const { show, hide } = useFloatingTooltip(`tooltip-${useId()}`, content, {
		position: 'custom',
		animation: { type: 'fade', duration: 150 },
		...options,
	});

	const handleMouseEnter = () => {
		if (triggerRef) {
			// Calculate optimal position based on trigger element
			const rect = triggerRef.getBoundingClientRect();
			const coordinates = calculateTooltipPosition(rect);

			// Update tooltip position
			update(id, { coordinates });
			show();
		}
	};

	const handleMouseLeave = () => {
		setTimeout(() => hide(), 100); // Small delay for better UX
	};

	return {
		triggerProps: {
			ref: setTriggerRef,
			onMouseEnter: handleMouseEnter,
			onMouseLeave: handleMouseLeave,
		},
	};
}
```

## API Routes Documentation

### Authentication Endpoints

#### POST /api/auth/login

**Mô tả**: Đăng nhập bằng username/password

**Request Body**:

```typescript
{
	username: string; // Min 3 characters
	password: string; // Min 6 characters
}
```

**Response**:

```typescript
// Success (200)
{
	success: true;
	message: string;
}

// Error (400/401)
{
	error: string;
}
```

**Middleware**: Error handling, validation, rate limiting

#### POST /api/auth/google-login

**Mô tả**: Đăng nhập bằng Google OAuth

**Request Body**:

```typescript
{
	credential: string; // Google JWT token
}
```

#### POST /api/auth/telegram-login

**Mô tả**: Đăng nhập bằng Telegram

**Request Body**:

```typescript
{
  id: number;
  first_name: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}
```

### Collection Management

#### GET /api/collections

**Mô tả**: Lấy danh sách collections của user

**Query Parameters**:

-   `page?: number` - Trang hiện tại (default: 1)
-   `limit?: number` - Số items per page (default: 10)
-   `search?: string` - Tìm kiếm theo tên
-   `language?: 'EN' | 'VI'` - Filter theo ngôn ngữ

**Response**:

```typescript
{
  collections: CollectionWithDetail[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

#### POST /api/collections

**Mô tả**: Tạo collection mới

**Request Body**:

```typescript
{
  name: string;
  description?: string;
  language: 'EN' | 'VI';
  difficulty?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  isPublic?: boolean;
}
```

#### GET /api/collections/[id]

**Mô tả**: Lấy chi tiết collection

**Response**: `CollectionWithDetail`

#### PUT /api/collections/[id]

**Mô tả**: Cập nhật collection

#### DELETE /api/collections/[id]

**Mô tả**: Xóa collection

### Word Management

#### GET /api/words

**Mô tả**: Lấy danh sách words

**Query Parameters**:

-   `collectionId?: string` - Filter theo collection
-   `language?: 'EN' | 'VI'` - Filter theo ngôn ngữ
-   `difficulty?: string` - Filter theo độ khó
-   `search?: string` - Tìm kiếm

#### POST /api/words

**Mô tả**: Tạo word mới

**Request Body**:

```typescript
{
  term: string;
  definition: string;
  pronunciation?: string;
  language: 'EN' | 'VI';
  collectionId: string;
  examples?: string[];
  synonyms?: string[];
  antonyms?: string[];
}
```

### LLM Integration

#### POST /api/llm/generate-words

**Mô tả**: Generate words bằng AI

**Request Body**:

```typescript
{
  keywords: string[];
  max_terms: number;
  source_language: 'EN' | 'VI';
  target_language: 'EN' | 'VI';
  exclude_collection_ids?: string[];
}
```

#### POST /api/llm/generate-paragraph

**Mô tả**: Generate paragraph với words

**Request Body**:

```typescript
{
  words: string[];
  language: 'EN' | 'VI';
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  topic?: string;
}
```

#### POST /api/llm/generate-questions

**Mô tả**: Generate questions cho paragraph

**Request Body**:

```typescript
{
	paragraph: string;
	language: 'EN' | 'VI';
	question_count: number;
	difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
}
```

### Statistics & Analytics

#### GET /api/collections/[id]/stats

**Mô tả**: Lấy thống kê collection

**Query Parameters**:

-   `days?: number` - Số ngày thống kê (default: 30)

**Response**:

```typescript
{
	totalWords: number;
	practiceCount: number;
	accuracyRate: number;
	dailyStats: Array<{
		date: string;
		wordReviews: number;
		qaPractice: number;
		paragraphPractice: number;
	}>;
}
```

#### POST /api/collections/[id]/stats/track

**Mô tả**: Track user activity

**Request Body**:

```typescript
{
  type: 'word_review' | 'qa_practice' | 'paragraph_practice';
  count?: number; // Default: 1
}
```

### Error Handling Pattern

Tất cả API routes sử dụng consistent error handling:

```typescript
// Success response
{
  data?: any;
  message?: string;
  success?: boolean;
}

// Error response
{
  error: string;
  code?: string;
  details?: any;
  timestamp: string;
  path: string;
  method: string;
}
```

### Middleware Stack

1. **Authentication**: JWT verification cho protected routes
2. **Validation**: Zod schema validation
3. **Rate Limiting**: IP-based throttling
4. **Error Handling**: Centralized error processing
5. **CORS**: Cross-origin request handling
6. **Security Headers**: XSS, CSRF protection

### Rate Limiting

-   **Authentication endpoints**: 5 requests/minute
-   **LLM endpoints**: 10 requests/minute
-   **General API**: 100 requests/minute
-   **File uploads**: 3 requests/minute

## Best Practices

### 1. Performance

-   Sử dụng `useCallback` và `useMemo` cho expensive operations
-   Lazy load floating content khi có thể
-   Debounce user interactions
-   Cleanup event listeners và timers

### 2. Accessibility

-   Luôn provide meaningful aria-labels
-   Implement proper focus management
-   Support keyboard navigation
-   Test với screen readers

### 3. Error Handling

-   Graceful degradation khi floating system fails
-   Provide fallback UI cho critical features
-   Log errors cho debugging
-   User-friendly error messages

### 4. Testing

-   Unit tests cho hooks và utilities
-   Integration tests cho floating interactions
-   Visual regression tests cho UI consistency
-   Accessibility testing với automated tools

Tài liệu này cung cấp cái nhìn toàn diện về hệ thống floating UI và API structure của dự án Vocab. Để biết thêm chi tiết implementation, tham khảo source code trong các thư mục tương ứng.
