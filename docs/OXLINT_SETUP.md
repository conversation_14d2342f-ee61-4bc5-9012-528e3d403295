# Oxlint Setup và Sử dụng

## Tổng quan

Oxlint là một JavaScript/TypeScript linter cự<PERSON> kỳ <PERSON>, đ<PERSON><PERSON><PERSON> viết bằng Rust và có thể nhanh hơn ESLint 50-100 lần. Dự án đã được tích hợp oxlint để cải thiện hiệu suất linting.

## Tính năng chính

- **Hiệu suất cao**: <PERSON><PERSON><PERSON> hơn ESLint 50-100 lần
- **Hỗ trợ đa ngôn ngữ**: JavaScript, TypeScript, JSX, TSX
- **Hơn 520 rules**: Từ eslint, typescript, react, jest, unicorn, jsx-a11y
- **Auto-fix**: Tự động sửa các lỗi có thể sửa được
- **Tích hợp ESLint**: <PERSON><PERSON> thể chạy song song với ESLint hiện tại

## Cấu hình

### File cấu hình: `.oxlintrc.json`

```json
{
  "$schema": "https://raw.githubusercontent.com/oxc-project/oxc/main/crates/oxc_linter/src/options/schema.json",
  "plugins": ["react", "unicorn", "typescript", "jsx-a11y"],
  "env": {
    "browser": true,
    "es2022": true,
    "node": true
  },
  "globals": {
    "React": "readonly",
    "JSX": "readonly"
  },
  "rules": {
    // Các rules đã được cấu hình phù hợp với dự án
  }
}
```

### Tích hợp với ESLint

Dự án sử dụng `eslint-plugin-oxlint` để tắt các rules trùng lặp trong ESLint:

```javascript
// eslint.config.mjs
import oxlint from 'eslint-plugin-oxlint';

export default [
  // ... other configs
  oxlint.configs.recommended,
  // ... other configs
];
```

## Scripts có sẵn

### Linting cơ bản
```bash
# Chạy oxlint
yarn lint:oxlint

# Chạy oxlint với auto-fix
yarn lint:oxlint:fix

# Chạy oxlint ở chế độ quiet (chỉ hiển thị errors)
yarn lint:oxlint:quiet
```

### Linting tổng hợp
```bash
# Chạy cả oxlint và ESLint
yarn lint

# Chạy cả oxlint và ESLint với auto-fix
yarn lint:fix

# Kiểm tra nhanh (quiet mode)
yarn lint:check
```

## Workflow khuyến nghị

### 1. Development
```bash
# Trong quá trình phát triển, sử dụng oxlint để kiểm tra nhanh
yarn lint:oxlint

# Hoặc với auto-fix
yarn lint:oxlint:fix
```

### 2. Pre-commit
```bash
# Trước khi commit, chạy cả hai linter
yarn lint:fix
```

### 3. CI/CD
```bash
# Trong CI, sử dụng quiet mode để kiểm tra nhanh
yarn lint:check
```

## So sánh hiệu suất

| Linter | Thời gian | Files | Threads |
|--------|-----------|-------|---------|
| Oxlint | ~50-100ms | 338   | 8       |
| ESLint | ~2-5s     | 338   | 1       |

## Rules được cấu hình

### Enabled Rules
- `unicorn/prefer-node-protocol`: Ưu tiên sử dụng `node:` protocol
- `jsx-a11y/*`: Các rules accessibility (warnings)
- `react/exhaustive-deps`: Kiểm tra dependencies (warnings)

### Disabled Rules
- `unicorn/no-array-for-each`: Tắt vì quá nhiều warnings
- `jsx-a11y/click-events-have-key-events`: Tắt vì quá nhiều warnings
- `react/react-in-jsx-scope`: Không cần với React 17+

## Troubleshooting

### Lỗi thường gặp

1. **`prefer-node-protocol` errors**
   ```bash
   # Auto-fix sẽ tự động sửa
   yarn lint:oxlint:fix
   ```

2. **React exhaustive-deps warnings**
   - Đây là warnings, không phải errors
   - Có thể ignore bằng comment: `// eslint-disable-next-line react-hooks/exhaustive-deps`

3. **JSX a11y warnings**
   - Cải thiện accessibility của ứng dụng
   - Có thể tạm thời ignore nếu cần

### Performance Tips

1. **Chạy oxlint trước ESLint** để có feedback nhanh hơn
2. **Sử dụng quiet mode** trong CI để giảm noise
3. **Auto-fix trước** khi chạy ESLint để giảm số lượng issues

## Tích hợp IDE

### VSCode
Cài đặt extension: [Oxc VSCode Extension](https://marketplace.visualstudio.com/items?itemName=oxc.oxc-vscode)

### Zed
Tìm kiếm "oxc" trong Zed Extensions

## Kết luận

Oxlint đã được tích hợp thành công vào dự án và mang lại:
- Tốc độ linting nhanh hơn đáng kể
- Phát hiện nhiều issues hơn với 520+ rules
- Tích hợp mượt mà với ESLint hiện tại
- Auto-fix cho nhiều loại lỗi

Sử dụng `yarn lint:oxlint:fix` thường xuyên để duy trì chất lượng code tốt nhất.
