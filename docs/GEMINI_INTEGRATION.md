# Gemini Integration with Firebase Genkit

This document explains how to use Google's Gemini AI models alongside OpenAI in the Vocab application using Firebase Genkit framework.

## Overview

The Vocab application now supports both OpenAI and Google Gemini as LLM providers through Firebase Genkit. Genkit provides a unified interface for multiple AI models with built-in observability, error handling, and advanced features. The system automatically selects the appropriate provider based on configuration and model requirements.

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Gemini Configuration
LLM_GEMINI_API_KEY=your_gemini_api_key_here
LLM_GEMINI_MODEL=gemini-1.5-flash
LLM_DEFAULT_PROVIDER=openai  # or 'gemini'

# Existing OpenAI Configuration
LLM_OPENAI_API_KEY=your_openai_api_key_here
LLM_OPENAI_MODEL=gpt-4o-mini
```

### 2. Getting Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to your environment variables

### 3. Firebase Genkit Setup

The application uses Firebase Genkit for unified AI model management:

-   **Genkit Core**: Provides the main framework and utilities
-   **Google AI Plugin**: Enables Gemini model integration
-   **Flow Management**: Handles complex AI workflows
-   **Built-in Observability**: Automatic logging and monitoring

## Available Models

### Gemini Models

-   **gemini-1.5-flash**: Fast, cost-effective model for most tasks
-   **gemini-1.5-pro**: High-quality model for complex tasks
-   **gemini-pro**: Standard model for general use

### Model Selection Logic

The system automatically selects the provider based on:

1. **Model prefix**: If a model name starts with `gemini`, Genkit with Gemini will be used
2. **Default provider**: Set via `LLM_DEFAULT_PROVIDER` environment variable
3. **Availability**: Falls back to available provider if the preferred one is not configured
4. **Genkit Features**: Automatic retry, error handling, and observability

## Usage

### Automatic Provider Selection

The LLMService automatically handles provider selection through Genkit. No code changes are required for existing functionality. Genkit provides additional benefits like automatic retries, structured logging, and performance monitoring.

```typescript
// This will use the configured default provider
const result = await llmService.generateRandomTerms({
	target_language: Language.EN,
	source_language: Language.VI,
	keywords: ['technology'],
	excludes: [],
	exclude_collections: [],
	userId: 'user123',
	count: 10,
});
```

### Force Specific Provider

You can force a specific provider by using model names. Genkit automatically handles the underlying API calls:

```typescript
// Force Gemini usage through Genkit
const geminiResult = await llmService.optimizedLLMCall(
	'generateText',
	'template-key',
	{ systemPrompt: 'Generate text...' },
	{
		model: 'gemini-1.5-flash', // Genkit handles this automatically
		temperature: 0.7,
		max_tokens: 1000,
	}
);

// Force OpenAI usage (traditional approach)
const openaiResult = await llmService.optimizedLLMCall(
	'generateText',
	'template-key',
	{ systemPrompt: 'Generate text...' },
	{
		model: 'gpt-4o-mini',
		temperature: 0.7,
		max_tokens: 1000,
	}
);
```

## Genkit Benefits

### Advanced Features

Firebase Genkit provides several advantages over direct API integration:

1. **Unified Interface**: Single API for multiple AI providers
2. **Built-in Retry Logic**: Automatic retry with exponential backoff
3. **Error Handling**: Comprehensive error handling and recovery
4. **Observability**: Built-in logging, tracing, and metrics
5. **Flow Management**: Complex AI workflow orchestration
6. **Type Safety**: Full TypeScript support with type inference
7. **Caching**: Intelligent response caching
8. **Rate Limiting**: Built-in rate limiting and quota management

### Performance Improvements

-   **Automatic Optimization**: Genkit optimizes API calls automatically
-   **Connection Pooling**: Efficient connection management
-   **Response Streaming**: Support for streaming responses
-   **Batch Processing**: Efficient batch request handling

## Configuration Options

### Provider Priority

Set your preferred default provider:

```bash
# Use Gemini as default
LLM_DEFAULT_PROVIDER=gemini

# Use OpenAI as default
LLM_DEFAULT_PROVIDER=openai
```

### Model-Specific Configuration

Different models have different capabilities and costs:

| Model            | Provider | Speed      | Quality    | Cost       | Best For                 |
| ---------------- | -------- | ---------- | ---------- | ---------- | ------------------------ |
| gemini-1.5-flash | Gemini   | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐   | ⭐⭐⭐⭐⭐ | Quick tasks, high volume |
| gemini-1.5-pro   | Gemini   | ⭐⭐⭐⭐   | ⭐⭐⭐⭐⭐ | ⭐⭐⭐     | Complex reasoning        |
| gpt-4o-mini      | OpenAI   | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐   | ⭐⭐⭐⭐   | Balanced performance     |
| gpt-4o           | OpenAI   | ⭐⭐⭐     | ⭐⭐⭐⭐⭐ | ⭐⭐       | Highest quality          |

## Error Handling

The system includes automatic fallback mechanisms:

1. **Provider Fallback**: If Gemini is unavailable, falls back to OpenAI
2. **Error Recovery**: Graceful handling of API errors
3. **Configuration Validation**: Validates API keys on startup

## Monitoring

### Token Usage

Both providers are monitored for token usage:

```typescript
// Token usage is automatically tracked
tokenMonitor.trackUsage({
	endpoint: 'generateText',
	operation: 'vocabulary',
	inputTokens: 150,
	outputTokens: 75,
	model: 'gemini-1.5-flash',
	userId: 'user123',
});
```

### Performance Metrics

Model performance is tracked and used for automatic selection:

-   **Latency**: Response time
-   **Quality**: Success rate
-   **Cost**: Token costs
-   **Reliability**: Error rates

## Testing

### Unit Tests

Run Gemini-specific tests:

```bash
yarn test src/backend/services/gemini.service.test.ts
```

### Integration Tests

Run provider integration tests:

```bash
yarn test src/backend/services/llm-provider-integration.test.ts
```

## Troubleshooting

### Common Issues

1. **API Key Not Working**

    - Verify the API key is correct
    - Check if the key has proper permissions
    - Ensure billing is enabled for your Google Cloud project

2. **Model Not Found**

    - Verify the model name is correct
    - Check if the model is available in your region

3. **Rate Limiting**
    - Implement proper retry logic
    - Consider using different models for different use cases

### Debug Mode

Enable debug logging:

```bash
LLM_LOG_LEVEL=debug
```

## Best Practices

1. **Cost Optimization**

    - Use `gemini-1.5-flash` for high-volume, simple tasks
    - Use `gemini-1.5-pro` for complex reasoning
    - Monitor token usage regularly

2. **Performance**

    - Cache frequently used results
    - Use batch processing when possible
    - Choose models based on latency requirements

3. **Reliability**
    - Always configure both providers for fallback
    - Implement proper error handling
    - Monitor API quotas and limits

## Migration Guide

### From OpenAI Only

1. Add Gemini environment variables
2. Set `LLM_DEFAULT_PROVIDER=gemini` if desired
3. Test with a subset of traffic
4. Monitor performance and costs
5. Gradually increase Gemini usage

### Configuration Examples

#### Development Environment

```bash
LLM_DEFAULT_PROVIDER=gemini
LLM_GEMINI_MODEL=gemini-1.5-flash
LLM_OPENAI_MODEL=gpt-4o-mini
```

#### Production Environment

```bash
LLM_DEFAULT_PROVIDER=openai
LLM_GEMINI_MODEL=gemini-1.5-pro
LLM_OPENAI_MODEL=gpt-4o
```

## Support

For issues related to Gemini integration:

1. Check the logs for error messages
2. Verify API key configuration
3. Test with simple requests first
4. Consult Google AI documentation for model-specific issues

## Future Enhancements

Planned improvements:

-   [ ] Automatic model selection based on task complexity
-   [ ] Cost optimization algorithms
-   [ ] Advanced caching strategies
-   [ ] Multi-region support
-   [ ] Custom model fine-tuning support
